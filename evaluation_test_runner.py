#!/usr/bin/env python3
"""
Evaluation Test Runner
=====================

This script uses runner_rest.py functionality to test all evaluation test cases
from evaluation_test_set.txt and saves the results for analysis.

Usage:
    python evaluation_test_runner.py --sequential
    python evaluation_test_runner.py --parallel --max-workers 5
    python evaluation_test_runner.py --category "TALK-BACK" --output results.json
"""

import requests
import json
import time
import re
import argparse
import threading
import subprocess
from concurrent.futures import ThreadPoolExecutor, as_completed
from uuid import uuid4
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timezone

# Configuration Constants (from runner_rest.py)
BASIC_URL = "http://localhost"
PATH_INITIATE = "conversation/initiate_chat"
PATH_SEND = "conversation/send_message"
PATH_RECEIVE = "conversation/get_message"
DISCONNECT_PATH = "conversation/end_chat"

HEADERS = {
    "Content-Type": "application/json",
    "access_token": "Test@123",
    "application-id": "application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3",
    "channel": "voice",
    "language": "en-US",
}

# Event Information
CHANNEL_INFO = {"channel_id": "voice", "channel_name": "voice"}
USER_INFO = {"UserName": "TestUser"}
USER_CONTEXT = {
    "UserName": "TestUser",
    "PhoneNumber": "9422139024",
    "WelcomeMessageFlag": "default",
    "Location": "Test Location",
    "BusinessId": "test_business_id",
}
LLM_CONTEXT = {
    "Customer's Dialed Location": "Test Location",
    "Customer's Dialed Number": "9422139024",
    "Customer's Name": "TestUser",
}

# Use RozieAir_V2 for testing (with triage agent V2)
ROSTER_ID = "RozieAir"

# Alternative roster for V1 testing
ROSTER_ID_V1 = "RozieAir"

# Docker container ID for log capture
DOCKER_CONTAINER_ID = "c1f45f289681"


@dataclass
class TestCase:
    """Individual test case from evaluation set."""
    test_id: str
    category: str
    user_input: str
    expected_activity: str
    conversation_sequence: List[str] = None
    test_type: str = "single"  # single, sequence, complex


@dataclass
class TestResult:
    """Result of a test case execution."""
    test_case: TestCase
    chat_id: str
    agent_responses: List[str]
    execution_time: float
    success: bool
    error_message: Optional[str] = None
    raw_responses: List[Dict] = None
    docker_logs: Optional[Dict] = None


class EvaluationTestRunner:
    """Runner for evaluation test cases."""

    def __init__(self, base_url: str = BASIC_URL, roster_id: str = ROSTER_ID, capture_logs: bool = True):
        """Initialize the test runner."""
        self.base_url = base_url
        self.roster_id = roster_id
        self.test_cases = []
        self.results = []
        self.capture_logs = capture_logs
        self.container_id = DOCKER_CONTAINER_ID
        
    def load_test_cases(self, file_path: str = "evaluation_test_set.txt") -> List[TestCase]:
        """Load test cases from evaluation_test_set.txt file."""
        print(f"📋 Loading test cases from {file_path}...")
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        test_cases = []
        
        # Parse single test cases (TEST_XXX format)
        single_test_pattern = r'TEST_(\d+)(?:\s*-\s*(.+?))?\nUser Input:\s*"(.+?)"\nExpected Triage Activity:\s*(.+?)(?=\n\n|\nTEST_|\nSCENARIO_|\nCOMPLEX_|\nEDGE_|\nSTRESS_|\nREALISTIC_|\nBUSINESS_|\nINTEGRATION_|\nUNKNOWN_|\nTRANSFER_|\nADDITIONAL|\nCATEGORY|\nCONVERSATION|\nEVALUATION|$)'
        
        for match in re.finditer(single_test_pattern, content, re.DOTALL):
            test_id = f"TEST_{match.group(1)}"
            category = match.group(2) if match.group(2) else "GENERAL"
            user_input = match.group(3)
            expected_activity = match.group(4).strip()
            
            test_cases.append(TestCase(
                test_id=test_id,
                category=category,
                user_input=user_input,
                expected_activity=expected_activity,
                test_type="single"
            ))
        
        # Parse conversation sequence scenarios
        scenario_pattern = r'(SCENARIO_\d+|COMPLEX_\d+|UNKNOWN_\d+|TRANSFER_\d+):\s*(.+?)\n((?:User_\d+:\s*"[^"]+"\n?)+)Expected Triage Activity:\s*(.+?)(?=\n\n|\nSCENARIO_|\nCOMPLEX_|\nEDGE_|\nSTRESS_|\nREALISTIC_|\nBUSINESS_|\nINTEGRATION_|\nUNKNOWN_|\nTRANSFER_|$)'
        
        for match in re.finditer(scenario_pattern, content, re.DOTALL):
            test_id = match.group(1)
            category = match.group(2)
            user_sequence_text = match.group(3)
            expected_activity = match.group(4).strip()
            
            # Extract user inputs from sequence
            user_inputs = re.findall(r'User_\d+:\s*"([^"]+)"', user_sequence_text)
            
            if user_inputs:
                test_cases.append(TestCase(
                    test_id=test_id,
                    category=category,
                    user_input=user_inputs[-1],  # Use last input as main input
                    expected_activity=expected_activity,
                    conversation_sequence=user_inputs,
                    test_type="sequence"
                ))
        
        # Parse stress test and other special cases
        special_pattern = r'(STRESS_\w*\d+|REALISTIC_\w*\d+|BUSINESS_\d+|INTEGRATION_\d+|EDGE_\d+):\s*(.+?)\nUser Input:\s*"(.+?)"\nExpected Triage Activity:\s*(.+?)(?=\n\n|\nSTRESS_|\nREALISTIC_|\nBUSINESS_|\nINTEGRATION_|\nEDGE_|\nSCENARIO_|\nCOMPLEX_|\nUNKNOWN_|\nTRANSFER_|$)'
        
        for match in re.finditer(special_pattern, content, re.DOTALL):
            test_id = match.group(1)
            category = match.group(2)
            user_input = match.group(3)
            expected_activity = match.group(4).strip()
            
            test_cases.append(TestCase(
                test_id=test_id,
                category=category,
                user_input=user_input,
                expected_activity=expected_activity,
                test_type="special"
            ))
        
        self.test_cases = test_cases
        print(f"✅ Loaded {len(test_cases)} test cases")
        return test_cases
    
    def get_base_event(self, chat_id: str):
        """Build base event payload."""
        return {
            "version": "1.0",
            "user_info": {
                "user_id": {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"},
                "user_info": {**USER_INFO},
            },
            "channel": {**CHANNEL_INFO, "ui_info": {"should_consolidate_buttons": False}},
            "incoming_events": [
                {
                    "event_id": "initiate_event",
                    "event_user": {
                        "user_id": {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"},
                        "user_info": {**USER_INFO},
                    },
                    "event_template": {},
                }
            ],
        }
    
    def get_initiate_event(self, chat_id: str):
        """Build initiate chat event payload."""
        initiate_event = self.get_base_event(chat_id)
        initiate_event["incoming_events"][0]["event_template"] = {
            "event_type": "initiate",
            "rosters_id": self.roster_id,
            "llm_context": LLM_CONTEXT,
            "user_context": USER_CONTEXT,
        }
        return initiate_event
    
    def get_send_message_event(self, user_input: str, chat_id: str):
        """Build send message event payload."""
        send_message_event = self.get_base_event(chat_id)
        send_message_event["incoming_events"][0]["event_template"] = {
            "event_type": "text",
            "text": user_input,
        }
        return send_message_event
    
    def get_receive_message_event(self, chat_id: str):
        """Build receive message event payload."""
        receive_message_event = self.get_base_event(chat_id)
        receive_message_event["incoming_events"][0]["event_template"] = {
            "event_type": "listen"
        }
        return receive_message_event
    
    def get_disconnect_event(self, chat_id: str):
        """Build disconnect event payload."""
        disconnect_event = self.get_base_event(chat_id)
        disconnect_event["incoming_events"][0]["event_template"] = {
            "event_type": "disconnect"
        }
        return disconnect_event

    def capture_docker_logs(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """Capture Docker logs for the specified time range."""
        if not self.capture_logs:
            return {"enabled": False}

        try:
            # Format timestamps for Docker logs (ISO format)
            start_str = start_time.strftime("%Y-%m-%dT%H:%M:%S")
            end_str = end_time.strftime("%Y-%m-%dT%H:%M:%S")

            # Capture logs using docker logs command
            cmd = [
                "docker", "logs", self.container_id,
                "--since", start_str,
                "--until", end_str,
                "--timestamps"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                raw_logs = result.stdout
                return {
                    "enabled": True,
                    "start_time": start_str,
                    "end_time": end_str,
                    "raw_logs": raw_logs,
                    "parsed_analysis": self.parse_agent_logs(raw_logs)
                }
            else:
                return {
                    "enabled": True,
                    "error": f"Docker logs failed: {result.stderr}",
                    "start_time": start_str,
                    "end_time": end_str
                }

        except Exception as e:
            return {
                "enabled": True,
                "error": f"Log capture failed: {str(e)}",
                "start_time": start_time.isoformat() if start_time else None,
                "end_time": end_time.isoformat() if end_time else None
            }

    def parse_agent_logs(self, raw_logs: str) -> Dict[str, Any]:
        """Parse Docker logs to extract agent routing and specialist information."""
        analysis = {
            "triage_agent_activity": [],
            "routing_decisions": [],
            "specialist_usage": [],
            "llm_interactions": [],
            "delegate_tools": [],
            "error_messages": [],
            "agent_version": "unknown",
            "agent_type": "unknown",
            "processing_summary": {}
        }

        lines = raw_logs.split('\n')

        # First pass: Detect agent version and type
        for line in lines:
            if "Using Enhanced Triage Agent V2" in line:
                analysis["agent_version"] = "V2"
                analysis["agent_type"] = "Enhanced Triage Agent V2 (Shubham's Requirements)"
            elif "Using Original Triage Agent V1" in line:
                analysis["agent_version"] = "V1"
                analysis["agent_type"] = "Original Triage Agent V1 (Legacy)"
            elif "TriageAgentV2Wrapper" in line:
                analysis["agent_version"] = "V2"
            elif "TriageAgentWrapper" in line:
                analysis["agent_version"] = "V1"

        # Second pass: Parse logs based on agent version
        for line in lines:
            if not line.strip():
                continue

            # Parse V2-specific logs (detailed logging)
            if analysis["agent_version"] == "V2":
                self._parse_v2_logs(line, analysis)

            # Parse V1-specific logs (minimal logging)
            elif analysis["agent_version"] == "V1":
                self._parse_v1_logs(line, analysis)

            # Parse common logs for both versions
            self._parse_common_logs(line, analysis)

        # Generate processing summary
        analysis["processing_summary"] = self._generate_processing_summary(analysis)

        return analysis

    def _parse_v2_logs(self, line: str, analysis: Dict[str, Any]):
        """Parse V2-specific log patterns."""
        # Extract triage agent activity (V2 has detailed logging)
        if "[TRIAGE_V2_HANDLER]" in line:
            if "Processing message" in line:
                analysis["triage_agent_activity"].append({
                    "action": "processing_message",
                    "log": line.strip()
                })
            elif "Conversation history:" in line:
                analysis["triage_agent_activity"].append({
                    "action": "conversation_history",
                    "log": line.strip()
                })
            elif "Latest message:" in line:
                analysis["triage_agent_activity"].append({
                    "action": "latest_message",
                    "log": line.strip()
                })
            elif "Available tools:" in line:
                analysis["triage_agent_activity"].append({
                    "action": "available_tools",
                    "log": line.strip()
                })
            elif "Delegate tools:" in line:
                # Extract delegate tools list
                tools_match = re.search(r"Delegate tools: (\[.*?\])", line)
                if tools_match:
                    try:
                        tools = eval(tools_match.group(1))  # Safe since it's our own log
                        analysis["delegate_tools"] = tools
                    except:
                        pass
                analysis["triage_agent_activity"].append({
                    "action": "delegate_tools",
                    "log": line.strip()
                })
            elif "LLM response received" in line:
                analysis["llm_interactions"].append({
                    "action": "response_received",
                    "log": line.strip()
                })
            elif "Response text:" in line:
                analysis["llm_interactions"].append({
                    "action": "response_text",
                    "log": line.strip()
                })
            elif "Sending final response" in line:
                analysis["triage_agent_activity"].append({
                    "action": "sending_response",
                    "log": line.strip()
                })
            elif "Message processing complete" in line:
                analysis["triage_agent_activity"].append({
                    "action": "processing_complete",
                    "log": line.strip()
                })

    def _parse_v1_logs(self, line: str, analysis: Dict[str, Any]):
        """Parse V1-specific log patterns."""
        # V1 has minimal logging, so we infer behavior from available logs
        if "TriageAgentWrapper" in line:
            analysis["triage_agent_activity"].append({
                "action": "agent_created",
                "log": line.strip()
            })

        # Look for any routing or tool usage patterns in V1
        if "route_to_" in line:
            analysis["routing_decisions"].append({
                "decision": "routing_detected",
                "log": line.strip()
            })

        # V1 doesn't have detailed delegate tools logging, so we infer from responses
        if "quick_reply" in line and "items" in line:
            analysis["triage_agent_activity"].append({
                "action": "quick_reply_options_provided",
                "log": "V1 agent provided quick reply options (inferred from response)"
            })

    def _parse_common_logs(self, line: str, analysis: Dict[str, Any]):
        """Parse log patterns common to both V1 and V2."""
        # Extract routing decisions
        if "route_to_" in line and "specialist" in line:
            analysis["routing_decisions"].append({
                "decision": "routing_to_specialist",
                "log": line.strip()
            })

        # Extract specialist usage
        if "specialist" in line.lower() and ("activated" in line or "called" in line):
            analysis["specialist_usage"].append({
                "action": "specialist_activation",
                "log": line.strip()
            })

        # Extract error messages
        if "error" in line.lower() or "failed" in line.lower():
            analysis["error_messages"].append({
                "type": "error",
                "log": line.strip()
            })

    def _generate_processing_summary(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary of the agent processing."""
        summary = {
            "agent_version": analysis["agent_version"],
            "agent_type": analysis["agent_type"],
            "total_activities": len(analysis["triage_agent_activity"]),
            "routing_decisions_count": len(analysis["routing_decisions"]),
            "llm_interactions_count": len(analysis["llm_interactions"]),
            "errors_count": len(analysis["error_messages"]),
            "delegate_tools_available": len(analysis["delegate_tools"]) if analysis["delegate_tools"] else 0
        }

        # Add V1-specific insights
        if analysis["agent_version"] == "V1":
            summary["v1_insights"] = {
                "note": "V1 agent has minimal logging - behavior inferred from responses",
                "quick_reply_provided": any("quick_reply" in str(activity) for activity in analysis["triage_agent_activity"]),
                "routing_capability": "Basic routing without detailed logging"
            }

        # Add V2-specific insights
        elif analysis["agent_version"] == "V2":
            summary["v2_insights"] = {
                "note": "V2 agent has comprehensive logging with detailed processing steps",
                "shubham_requirements": "Talk-back, Clarification, Unknown-info handling, Mid-conversation transfer",
                "processing_steps_logged": True
            }

        return summary

    def execute_single_test(self, test_case: TestCase) -> TestResult:
        """Execute a single test case."""
        chat_id = f"TEST_{test_case.test_id}_{uuid4()}"
        headers = {**HEADERS, "rozie-correlation-id": chat_id}

        start_time = time.time()
        log_start_time = datetime.now()
        agent_responses = []
        raw_responses = []
        error_message = None
        success = False
        docker_logs = None

        try:
            print(f"🧪 Testing {test_case.test_id}: {test_case.user_input[:50]}...")

            # Step 1: Initiate chat
            url = f"{self.base_url}/{PATH_INITIATE}"
            response = requests.post(
                url, headers=headers, json=self.get_initiate_event(chat_id), timeout=30
            )

            if response.status_code != 200:
                raise Exception(f"Initiate failed: {response.status_code}")

            # Step 2: Wait for initial agent response
            time.sleep(1)

            # Step 3: Send user message(s)
            if test_case.conversation_sequence and test_case.test_type == "sequence":
                # Handle conversation sequence
                for i, user_input in enumerate(test_case.conversation_sequence):
                    print(f"  📝 Sending message {i+1}: {user_input}")

                    # Send message
                    url = f"{self.base_url}/{PATH_SEND}"
                    response = requests.post(
                        url, headers=headers, json=self.get_send_message_event(user_input, chat_id), timeout=30
                    )

                    if response.status_code != 200:
                        raise Exception(f"Send message failed: {response.status_code}")

                    # Get agent response
                    time.sleep(2)  # Wait for processing
                    agent_response = self._get_agent_response(chat_id, headers)
                    if agent_response:
                        agent_responses.extend(agent_response["responses"])
                        raw_responses.append(agent_response["raw"])
            else:
                # Handle single message
                url = f"{self.base_url}/{PATH_SEND}"
                response = requests.post(
                    url, headers=headers, json=self.get_send_message_event(test_case.user_input, chat_id), timeout=30
                )

                if response.status_code != 200:
                    raise Exception(f"Send message failed: {response.status_code}")

                # Get agent response
                time.sleep(2)  # Wait for processing
                agent_response = self._get_agent_response(chat_id, headers)
                if agent_response:
                    agent_responses.extend(agent_response["responses"])
                    raw_responses.append(agent_response["raw"])

            # Step 4: Disconnect
            url = f"{self.base_url}/{DISCONNECT_PATH}"
            requests.post(
                url, headers=headers, json=self.get_disconnect_event(chat_id), timeout=10
            )

            success = True

        except Exception as e:
            error_message = str(e)
            print(f"  ❌ Error: {error_message}")

        execution_time = time.time() - start_time
        log_end_time = datetime.now()

        # Capture Docker logs for this test execution
        if self.capture_logs:
            print(f"  📋 Capturing Docker logs from {log_start_time.strftime('%H:%M:%S')} to {log_end_time.strftime('%H:%M:%S')}...")
            docker_logs = self.capture_docker_logs(log_start_time, log_end_time)

            # Print summary of captured logs
            if docker_logs.get("enabled") and "parsed_analysis" in docker_logs:
                analysis = docker_logs["parsed_analysis"]
                print(f"  🔍 Captured {len(analysis.get('triage_agent_activity', []))} triage activities")
                print(f"  🛠️  Available tools: {len(analysis.get('delegate_tools', []))}")
                if analysis.get("routing_decisions"):
                    print(f"  🔀 Routing decisions: {len(analysis['routing_decisions'])}")

        return TestResult(
            test_case=test_case,
            chat_id=chat_id,
            agent_responses=agent_responses,
            execution_time=execution_time,
            success=success,
            error_message=error_message,
            raw_responses=raw_responses,
            docker_logs=docker_logs
        )

    def _get_agent_response(self, chat_id: str, headers: Dict) -> Optional[Dict]:
        """Get agent response with polling."""
        max_attempts = 10
        attempt = 0

        while attempt < max_attempts:
            try:
                url = f"{self.base_url}/{PATH_RECEIVE}"
                response = requests.post(
                    url, headers=headers, json=self.get_receive_message_event(chat_id), timeout=10
                )

                if response.status_code == 200:
                    data = response.json()
                    next_action = data.get("next_action")

                    if next_action == "wait":
                        time.sleep(0.5)
                        attempt += 1
                        continue
                    elif next_action in {"say", "gather"}:
                        # Extract agent responses
                        responses = []
                        response_map = data.get("response_map", {}).get("responses", {}).get("default", [])

                        for resp in response_map:
                            text = resp.get("response_template", {}).get("text")
                            if text:
                                responses.append(text)

                        return {"responses": responses, "raw": data}
                    elif next_action == "disconnect":
                        return {"responses": ["[DISCONNECT]"], "raw": data}

                attempt += 1
                time.sleep(0.5)

            except Exception as e:
                print(f"  ⚠️  Response polling error: {e}")
                attempt += 1
                time.sleep(0.5)

        return None

    def run_sequential_tests(self, test_cases: List[TestCase] = None) -> List[TestResult]:
        """Run tests sequentially."""
        if test_cases is None:
            test_cases = self.test_cases

        print(f"🔄 Running {len(test_cases)} tests sequentially...")
        results = []

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i}/{len(test_cases)}] {test_case.test_id}")
            result = self.execute_single_test(test_case)
            results.append(result)

            # Brief pause between tests
            time.sleep(1)

        self.results = results
        return results

    def run_parallel_tests(self, test_cases: List[TestCase] = None, max_workers: int = 5) -> List[TestResult]:
        """Run tests in parallel."""
        if test_cases is None:
            test_cases = self.test_cases

        print(f"🚀 Running {len(test_cases)} tests in parallel (max workers: {max_workers})...")
        results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all test cases
            future_to_test = {executor.submit(self.execute_single_test, test_case): test_case
                             for test_case in test_cases}

            # Collect results as they complete
            for i, future in enumerate(as_completed(future_to_test), 1):
                test_case = future_to_test[future]
                try:
                    result = future.result()
                    results.append(result)
                    status = "✅" if result.success else "❌"
                    print(f"[{i}/{len(test_cases)}] {status} {test_case.test_id}")
                except Exception as e:
                    print(f"[{i}/{len(test_cases)}] ❌ {test_case.test_id} - Exception: {e}")

        self.results = results
        return results

    def filter_test_cases(self, category: str = None, test_type: str = None) -> List[TestCase]:
        """Filter test cases by category or type."""
        filtered = self.test_cases

        if category:
            filtered = [tc for tc in filtered if category.upper() in tc.category.upper()]

        if test_type:
            filtered = [tc for tc in filtered if tc.test_type == test_type]

        return filtered

    def analyze_results(self) -> Dict[str, Any]:
        """Analyze test results and generate summary."""
        if not self.results:
            return {"error": "No results to analyze"}

        total_tests = len(self.results)
        successful_tests = len([r for r in self.results if r.success])
        failed_tests = total_tests - successful_tests

        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        avg_execution_time = sum(r.execution_time for r in self.results) / total_tests if total_tests > 0 else 0

        # Category breakdown
        category_stats = {}
        for result in self.results:
            category = result.test_case.category
            if category not in category_stats:
                category_stats[category] = {"total": 0, "success": 0, "failed": 0}

            category_stats[category]["total"] += 1
            if result.success:
                category_stats[category]["success"] += 1
            else:
                category_stats[category]["failed"] += 1

        # Calculate category success rates
        for category, stats in category_stats.items():
            stats["success_rate"] = (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0

        return {
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate,
                "avg_execution_time": avg_execution_time
            },
            "category_breakdown": category_stats,
            "failed_tests": [
                {
                    "test_id": r.test_case.test_id,
                    "category": r.test_case.category,
                    "error": r.error_message,
                    "user_input": r.test_case.user_input
                }
                for r in self.results if not r.success
            ]
        }

    def save_results(self, filename: str = None) -> str:
        """Save test results to JSON file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"evaluation_test_results_{timestamp}.json"

        # Prepare results for JSON serialization
        results_data = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "roster_id": self.roster_id,
                "base_url": self.base_url,
                "total_tests": len(self.results)
            },
            "analysis": self.analyze_results(),
            "detailed_results": [
                {
                    "test_id": r.test_case.test_id,
                    "category": r.test_case.category,
                    "test_type": r.test_case.test_type,
                    "user_input": r.test_case.user_input,
                    "expected_activity": r.test_case.expected_activity,
                    "conversation_sequence": r.test_case.conversation_sequence,
                    "chat_id": r.chat_id,
                    "agent_responses": r.agent_responses,
                    "execution_time": r.execution_time,
                    "success": r.success,
                    "error_message": r.error_message,
                    "raw_responses": r.raw_responses,
                    "docker_logs": r.docker_logs
                }
                for r in self.results
            ]
        }

        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2)

        print(f"💾 Results saved to: {filename}")
        return filename

    def print_summary(self):
        """Print test results summary."""
        analysis = self.analyze_results()

        print(f"\n📊 Test Results Summary")
        print("=" * 50)

        summary = analysis["summary"]
        print(f"Total Tests: {summary['total_tests']}")
        print(f"✅ Successful: {summary['successful_tests']}")
        print(f"❌ Failed: {summary['failed_tests']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Avg Execution Time: {summary['avg_execution_time']:.2f}s")

        print(f"\n📋 Category Breakdown:")
        for category, stats in analysis["category_breakdown"].items():
            print(f"  {category}: {stats['success']}/{stats['total']} ({stats['success_rate']:.1f}%)")

        if analysis["failed_tests"]:
            print(f"\n❌ Failed Tests:")
            for failed in analysis["failed_tests"][:5]:  # Show first 5 failures
                print(f"  - {failed['test_id']}: {failed['error']}")

            if len(analysis["failed_tests"]) > 5:
                print(f"  ... and {len(analysis['failed_tests']) - 5} more")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Evaluation Test Runner")
    parser.add_argument("--sequential", action="store_true", help="Run tests sequentially")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--max-workers", type=int, default=5, help="Max parallel workers")
    parser.add_argument("--category", type=str, help="Filter by category")
    parser.add_argument("--test-type", choices=["single", "sequence", "special"], help="Filter by test type")
    parser.add_argument("--output", type=str, help="Output filename for results")
    parser.add_argument("--roster-id", type=str, default=ROSTER_ID, help="Roster ID to use")
    parser.add_argument("--use-v1", action="store_true", help="Use V1 triage agent (RozieAir roster)")
    parser.add_argument("--base-url", type=str, default=BASIC_URL, help="Base URL for API")
    parser.add_argument("--capture-logs", action="store_true", default=True, help="Capture Docker logs during test execution")
    parser.add_argument("--no-logs", action="store_true", help="Disable Docker log capture")

    args = parser.parse_args()

    # Determine log capture setting
    capture_logs = args.capture_logs and not args.no_logs

    # Determine roster ID based on version selection
    roster_id = ROSTER_ID_V1 if args.use_v1 else args.roster_id

    # Print version info
    if args.use_v1:
        print("🔄 Using Triage Agent V1 (Legacy) with roster: RozieAir")
    else:
        print(f"🚀 Using Triage Agent V2 (Enhanced) with roster: {roster_id}")

    # Initialize runner
    runner = EvaluationTestRunner(base_url=args.base_url, roster_id=roster_id, capture_logs=capture_logs)

    # Load test cases
    test_cases = runner.load_test_cases()

    # Filter test cases if requested
    if args.category or args.test_type:
        test_cases = runner.filter_test_cases(args.category, args.test_type)
        print(f"🔍 Filtered to {len(test_cases)} test cases")

    if not test_cases:
        print("❌ No test cases found matching criteria")
        return

    # Run tests
    if args.parallel:
        results = runner.run_parallel_tests(test_cases, args.max_workers)
    else:
        results = runner.run_sequential_tests(test_cases)

    # Print summary
    runner.print_summary()

    # Save results
    output_file = runner.save_results(args.output)

    print(f"\n✅ Evaluation complete!")
    print(f"📁 Results saved to: {output_file}")


if __name__ == "__main__":
    main()
