# Evaluation Test Runner - Detailed Step-by-Step Workflow

## 🔍 **COMPLETE TEST EXECUTION WORKFLOW**

### **Phase 1: Initialization & Setup**

#### 1.1 **Command Line Parsing**
```bash
python3 evaluation_test_runner.py --category "GENERAL" --sequential --output results.json
```

**What happens:**
- Parses command line arguments
- Sets configuration: base URL, roster ID, execution mode
- Validates parameters

#### 1.2 **Test Runner Initialization**
```python
runner = EvaluationTestRunner(base_url="http://localhost", roster_id="RozieAir_V2")
```

**Configuration loaded:**
- `base_url`: "http://localhost" (Docker container)
- `roster_id`: "RozieAir_V2" (Triage Agent V2)
- `headers`: Authentication and channel setup

#### 1.3 **Test Case Loading**
```python
test_cases = runner.load_test_cases("evaluation_test_set.txt")
```

**Process:**
1. Opens `evaluation_test_set.txt`
2. Parses using regex patterns for different test formats:
   - `TEST_XXX` format for single tests
   - `SCENARIO_XXX` format for conversation sequences
   - `STRESS_XXX`, `EDGE_XXX` for special cases
3. Creates `TestCase` objects with:
   - `test_id`: Unique identifier
   - `category`: Test category (e.g., "GENERAL")
   - `user_input`: What user says
   - `expected_activity`: What triage agent should do
   - `conversation_sequence`: For multi-turn conversations
   - `test_type`: "single", "sequence", or "special"

**Result:** 76 test cases loaded into memory

### **Phase 2: Test Filtering & Selection**

#### 2.1 **Category Filtering**
```python
filtered_tests = runner.filter_test_cases(category="GENERAL")
```

**Process:**
- Searches through all test cases
- Matches category names (case-insensitive)
- Returns subset of matching tests

**Example:** 15 tests match "GENERAL" category

### **Phase 3: Individual Test Execution**

For each test case, the runner executes this detailed workflow:

#### 3.1 **Test Preparation**
```python
chat_id = f"TEST_{test_case.test_id}_{uuid4()}"
headers = {**HEADERS, "rozie-correlation-id": chat_id}
start_time = time.time()
```

**Setup:**
- Generates unique chat ID: `TEST_TEST_001_2ca0a6d2-80ca-4067-9e24-8d62d4f322f3`
- Adds correlation ID to headers for tracking
- Starts timing for performance measurement

#### 3.2 **Step 1: Initiate Chat Session**
```python
url = f"{self.base_url}/conversation/initiate_chat"
response = requests.post(url, headers=headers, json=initiate_event, timeout=30)
```

**Initiate Event Payload:**
```json
{
  "version": "1.0",
  "user_info": {
    "user_id": {"id": "TEST_TEST_001_...", "id_type": "chat_id", "id_resource": "chat"},
    "user_info": {"UserName": "TestUser"}
  },
  "channel": {"channel_id": "voice", "channel_name": "voice"},
  "incoming_events": [{
    "event_id": "initiate_event",
    "event_template": {
      "event_type": "initiate",
      "rosters_id": "RozieAir_V2",
      "llm_context": {"Customer's Name": "TestUser", ...},
      "user_context": {"UserName": "TestUser", "PhoneNumber": "9422139024", ...}
    }
  }]
}
```

**What happens in the API:**
1. Multi-agent framework receives initiate request
2. Creates new conversation session
3. Loads RozieAir_V2 roster configuration
4. Initializes Triage Agent V2
5. Returns success response with chat_id

#### 3.3 **Step 2: Wait for System Initialization**
```python
time.sleep(1)  # Allow system to initialize
```

**Purpose:** Gives the multi-agent system time to:
- Set up conversation context
- Initialize agent pipeline
- Prepare for incoming messages

#### 3.4 **Step 3: Send User Message**

**For Single Message Tests:**
```python
url = f"{self.base_url}/conversation/send_message"
response = requests.post(url, headers=headers, json=send_event, timeout=30)
```

**Send Message Event Payload:**
```json
{
  "version": "1.0",
  "user_info": {...},
  "channel": {...},
  "incoming_events": [{
    "event_template": {
      "event_type": "text",
      "text": "Hi there, I need some help"
    }
  }]
}
```

**For Conversation Sequence Tests:**
```python
for i, user_input in enumerate(test_case.conversation_sequence):
    # Send each message in sequence
    # Wait for response after each message
```

**What happens in the API:**
1. Receives user message
2. Routes to Triage Agent V2
3. Agent processes message using LLM
4. Determines appropriate response/routing
5. Queues response for retrieval

#### 3.5 **Step 4: Retrieve Agent Response**
```python
agent_response = self._get_agent_response(chat_id, headers)
```

**Response Polling Process:**
```python
def _get_agent_response(self, chat_id: str, headers: Dict):
    max_attempts = 10
    attempt = 0
    
    while attempt < max_attempts:
        url = f"{self.base_url}/conversation/get_message"
        response = requests.post(url, headers=headers, json=receive_event, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            next_action = data.get("next_action")
            
            if next_action == "wait":
                time.sleep(0.5)  # Agent still processing
                continue
            elif next_action in {"say", "gather"}:
                # Extract agent responses
                responses = []
                response_map = data.get("response_map", {}).get("responses", {}).get("default", [])
                
                for resp in response_map:
                    text = resp.get("response_template", {}).get("text")
                    if text:
                        responses.append(text)
                
                return {"responses": responses, "raw": data}
```

**Polling Logic:**
1. **Attempt 1-10:** Check if agent has response ready
2. **"wait" response:** Agent still processing, wait 0.5s and retry
3. **"say"/"gather" response:** Agent has response, extract text
4. **"disconnect" response:** Conversation ended
5. **Timeout:** After 10 attempts, return None

**Sample Agent Response:**
```json
{
  "version": "1.0",
  "response_map": {
    "responses": {
      "default": [{
        "response_template": {
          "response_type": "text",
          "text": "Hello! Thank you for reaching out to Rozie Airline. I'm here to assist you. Could you please tell me more about what you need help with?"
        }
      }]
    }
  },
  "next_action": "gather"
}
```

#### 3.6 **Step 5: Clean Session Disconnect**
```python
url = f"{self.base_url}/conversation/end_chat"
requests.post(url, headers=headers, json=disconnect_event, timeout=10)
```

**Purpose:**
- Properly closes conversation session
- Cleans up resources in multi-agent framework
- Prevents memory leaks

#### 3.7 **Step 6: Result Processing**
```python
execution_time = time.time() - start_time
return TestResult(
    test_case=test_case,
    chat_id=chat_id,
    agent_responses=agent_responses,
    execution_time=execution_time,
    success=True,
    error_message=None,
    raw_responses=raw_responses
)
```

**TestResult Object Contains:**
- Original test case data
- Unique chat ID used
- All agent responses received
- Execution time in seconds
- Success/failure status
- Error message (if any)
- Raw API responses for debugging

### **Phase 4: Execution Modes**

#### 4.1 **Sequential Execution**
```python
def run_sequential_tests(self, test_cases):
    for i, test_case in enumerate(test_cases, 1):
        print(f"[{i}/{len(test_cases)}] {test_case.test_id}")
        result = self.execute_single_test(test_case)
        results.append(result)
        time.sleep(1)  # Brief pause between tests
```

**Characteristics:**
- Tests run one after another
- Detailed progress reporting
- 1-second pause between tests
- Easier debugging
- Slower overall execution

#### 4.2 **Parallel Execution**
```python
def run_parallel_tests(self, test_cases, max_workers=5):
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_test = {executor.submit(self.execute_single_test, test_case): test_case
                         for test_case in test_cases}
        
        for future in as_completed(future_to_test):
            result = future.result()
            results.append(result)
```

**Characteristics:**
- Multiple tests run simultaneously
- Configurable worker count (default: 5)
- Faster overall execution
- Results collected as they complete
- More complex error handling

### **Phase 5: Results Analysis & Reporting**

#### 5.1 **Statistical Analysis**
```python
def analyze_results(self):
    total_tests = len(self.results)
    successful_tests = len([r for r in self.results if r.success])
    success_rate = (successful_tests / total_tests) * 100
    avg_execution_time = sum(r.execution_time for r in self.results) / total_tests
    
    # Category breakdown
    category_stats = {}
    for result in self.results:
        category = result.test_case.category
        # Calculate per-category success rates
```

#### 5.2 **JSON Export**
```python
results_data = {
    "metadata": {
        "timestamp": "2025-06-16T21:19:39.219869",
        "roster_id": "RozieAir_V2",
        "base_url": "http://localhost",
        "total_tests": 15
    },
    "analysis": {
        "summary": {
            "total_tests": 15,
            "successful_tests": 15,
            "success_rate": 100.0,
            "avg_execution_time": 4.53
        },
        "category_breakdown": {...},
        "failed_tests": []
    },
    "detailed_results": [...]
}
```

### **Phase 6: Error Handling**

#### 6.1 **Connection Errors**
- API server not running
- Network timeouts
- Authentication failures

#### 6.2 **API Errors**
- Invalid request format
- Missing required fields
- Server-side processing errors

#### 6.3 **Response Processing Errors**
- Malformed JSON responses
- Missing expected fields
- Timeout waiting for agent response

## 🎯 **REAL EXAMPLE: Complete Test Flow**

**Test Case:** `TEST_001`
**Input:** "Hi there, I need some help"
**Expected:** "Provide conversational response before routing, ask clarifying question"

**Execution Timeline:**
1. **0.0s:** Generate chat ID: `TEST_TEST_001_2ca0a6d2-80ca-4067-9e24-8d62d4f322f3`
2. **0.1s:** Send initiate request to `/conversation/initiate_chat`
3. **0.2s:** API creates session, loads RozieAir_V2, initializes Triage Agent V2
4. **1.2s:** Send user message: "Hi there, I need some help"
5. **1.3s:** Triage Agent V2 processes message, determines it needs clarification
6. **2.5s:** Start polling for response
7. **3.0s:** Receive agent response: "Hello! Thank you for reaching out to Rozie Airline..."
8. **3.1s:** Send disconnect request
9. **3.6s:** Test complete, execution time: 3.65 seconds

**Result:** ✅ SUCCESS - Agent provided conversational response and asked for clarification

This detailed workflow shows exactly how each test validates the triage agent's behavior against Shubham's requirements!
