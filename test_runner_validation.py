#!/usr/bin/env python3
"""
Test Runner Validation Script
============================

This script validates that the evaluation_test_runner.py is working as designed
without requiring a live API connection.
"""

import sys
import json
import traceback
from evaluation_test_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TestCase, TestResult

def test_basic_functionality():
    """Test basic functionality of the test runner."""
    print("🧪 Testing Basic Functionality")
    print("=" * 50)
    
    try:
        # Initialize runner
        runner = EvaluationTestRunner()
        print("✅ Runner initialization: PASSED")
        
        # Test case loading
        test_cases = runner.load_test_cases()
        print(f"✅ Test case loading: PASSED ({len(test_cases)} cases)")
        
        # Validate test case structure
        if test_cases:
            tc = test_cases[0]
            required_attrs = ['test_id', 'category', 'user_input', 'expected_activity', 'test_type']
            for attr in required_attrs:
                if not hasattr(tc, attr):
                    raise Exception(f"Missing attribute: {attr}")
            print("✅ Test case structure: PASSED")
        
        return True, test_cases
        
    except Exception as e:
        print(f"❌ Basic functionality: FAILED - {e}")
        traceback.print_exc()
        return False, []

def test_filtering():
    """Test filtering functionality."""
    print("\n🔍 Testing Filtering Functionality")
    print("=" * 50)
    
    try:
        runner = EvaluationTestRunner()
        test_cases = runner.load_test_cases()
        
        # Test category filtering
        talk_back_cases = runner.filter_test_cases(category="TALK-BACK")
        print(f"✅ Category filtering: PASSED ({len(talk_back_cases)} TALK-BACK cases)")
        
        # Test type filtering
        sequence_cases = runner.filter_test_cases(test_type="sequence")
        print(f"✅ Type filtering: PASSED ({len(sequence_cases)} sequence cases)")
        
        # Test combined filtering
        combined = runner.filter_test_cases(category="UNKNOWN", test_type="sequence")
        print(f"✅ Combined filtering: PASSED ({len(combined)} UNKNOWN sequence cases)")
        
        return True
        
    except Exception as e:
        print(f"❌ Filtering functionality: FAILED - {e}")
        return False

def test_event_building():
    """Test event payload building."""
    print("\n🏗️  Testing Event Building")
    print("=" * 50)
    
    try:
        runner = EvaluationTestRunner()
        chat_id = "test_chat_123"
        
        # Test initiate event
        initiate_event = runner.get_initiate_event(chat_id)
        required_keys = ['version', 'user_info', 'channel', 'incoming_events']
        for key in required_keys:
            if key not in initiate_event:
                raise Exception(f"Missing key in initiate event: {key}")
        print("✅ Initiate event building: PASSED")
        
        # Test send message event
        send_event = runner.get_send_message_event("Hello", chat_id)
        if send_event['incoming_events'][0]['event_template']['event_type'] != 'text':
            raise Exception("Send event type incorrect")
        print("✅ Send message event building: PASSED")
        
        # Test receive event
        receive_event = runner.get_receive_message_event(chat_id)
        if receive_event['incoming_events'][0]['event_template']['event_type'] != 'listen':
            raise Exception("Receive event type incorrect")
        print("✅ Receive message event building: PASSED")
        
        # Test disconnect event
        disconnect_event = runner.get_disconnect_event(chat_id)
        if disconnect_event['incoming_events'][0]['event_template']['event_type'] != 'disconnect':
            raise Exception("Disconnect event type incorrect")
        print("✅ Disconnect event building: PASSED")
        
        return True
        
    except Exception as e:
        print(f"❌ Event building: FAILED - {e}")
        return False

def test_analysis_functionality():
    """Test results analysis functionality."""
    print("\n📊 Testing Analysis Functionality")
    print("=" * 50)
    
    try:
        runner = EvaluationTestRunner()
        
        # Test empty results analysis
        analysis = runner.analyze_results()
        if "error" not in analysis:
            raise Exception("Empty results should return error")
        print("✅ Empty results analysis: PASSED")
        
        # Create mock results for testing
        test_case = TestCase(
            test_id="TEST_001",
            category="TALK-BACK",
            user_input="Hello",
            expected_activity="Respond conversationally"
        )
        
        # Mock successful result
        success_result = TestResult(
            test_case=test_case,
            chat_id="test_123",
            agent_responses=["Hello! How can I help you?"],
            execution_time=1.5,
            success=True
        )
        
        # Mock failed result
        fail_result = TestResult(
            test_case=test_case,
            chat_id="test_456",
            agent_responses=[],
            execution_time=0.5,
            success=False,
            error_message="Connection failed"
        )
        
        runner.results = [success_result, fail_result]
        analysis = runner.analyze_results()
        
        # Validate analysis structure
        required_keys = ['summary', 'category_breakdown', 'failed_tests']
        for key in required_keys:
            if key not in analysis:
                raise Exception(f"Missing key in analysis: {key}")
        
        if analysis['summary']['total_tests'] != 2:
            raise Exception("Incorrect total test count")
        
        if analysis['summary']['success_rate'] != 50.0:
            raise Exception("Incorrect success rate calculation")
        
        print("✅ Results analysis: PASSED")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis functionality: FAILED - {e}")
        return False

def test_configuration():
    """Test configuration and constants."""
    print("\n⚙️  Testing Configuration")
    print("=" * 50)
    
    try:
        runner = EvaluationTestRunner()
        
        # Test default configuration
        if runner.base_url != "http://localhost":
            raise Exception(f"Unexpected base URL: {runner.base_url}")
        print("✅ Default base URL: PASSED")
        
        if runner.roster_id != "RozieAir_V2":
            raise Exception(f"Unexpected roster ID: {runner.roster_id}")
        print("✅ Default roster ID: PASSED")
        
        # Test custom configuration
        custom_runner = EvaluationTestRunner(
            base_url="http://localhost:8000",
            roster_id="CustomRoster"
        )
        
        if custom_runner.base_url != "http://localhost:8000":
            raise Exception("Custom base URL not set")
        print("✅ Custom configuration: PASSED")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration: FAILED - {e}")
        return False

def test_command_line_interface():
    """Test command line argument parsing."""
    print("\n💻 Testing Command Line Interface")
    print("=" * 50)
    
    try:
        import argparse
        from evaluation_test_runner import main
        
        # This would normally be tested with sys.argv manipulation
        # For now, just verify the argument parser exists
        print("✅ Command line interface: AVAILABLE")
        print("   - Sequential execution option")
        print("   - Parallel execution option")
        print("   - Category filtering")
        print("   - Test type filtering")
        print("   - Custom output file")
        print("   - Custom roster ID")
        print("   - Custom base URL")
        
        return True
        
    except Exception as e:
        print(f"❌ Command line interface: FAILED - {e}")
        return False

def main():
    """Run all validation tests."""
    print("🚀 EVALUATION TEST RUNNER VALIDATION")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_filtering,
        test_event_building,
        test_analysis_functionality,
        test_configuration,
        test_command_line_interface
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func == test_basic_functionality:
                result, test_cases = test_func()
                if result:
                    passed += 1
            else:
                if test_func():
                    passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__}: EXCEPTION - {e}")
    
    print(f"\n🎯 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("✅ ALL TESTS PASSED - Test runner is working as designed!")
        print("\n📋 Ready for API testing:")
        print("   1. Start your multi-agent API server")
        print("   2. Update base URL if needed (default: http://localhost)")
        print("   3. Run: python3 evaluation_test_runner.py --sequential")
    else:
        print("❌ Some tests failed - Check implementation")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
