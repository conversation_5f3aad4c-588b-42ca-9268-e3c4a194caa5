#!/usr/bin/env python3
"""
Simple V1 Triage Agent Test
===========================

Test the V1 triage agent with error tolerance for service issues.
"""

import requests
import json
import time
from datetime import datetime

def test_v1_triage_agent():
    """Test V1 triage agent with a simple message."""
    
    print("🔄 Testing V1 Triage Agent (RozieAir roster)")
    print("=" * 50)
    
    base_url = "http://localhost"
    chat_id = f"V1_TEST_{int(time.time())}"
    
    headers = {
        "Content-Type": "application/json",
        "access_token": "Test@123",
        "application-id": "application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3",
        "channel": "voice",
        "language": "en-US",
        "rozie-correlation-id": chat_id
    }
    
    # Step 1: Initiate chat
    print("📞 Step 1: Initiating chat session...")
    initiate_payload = {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": chat_id,
                "id_type": "chat_id",
                "id_resource": "chat"
            },
            "user_info": {
                "UserName": "TestUser"
            }
        },
        "channel": {
            "channel_id": "voice",
            "channel_name": "voice"
        },
        "incoming_events": [{
            "event_id": "initiate_event",
            "event_template": {
                "event_type": "initiate",
                "rosters_id": "RozieAir",  # V1 roster
                "llm_context": {
                    "Customer's Name": "TestUser",
                    "Phone Number": "9422139024"
                },
                "user_context": {
                    "UserName": "TestUser",
                    "PhoneNumber": "9422139024",
                    "Channel": "voice"
                }
            }
        }]
    }
    
    try:
        response = requests.post(f"{base_url}/conversation/initiate_chat", 
                               headers=headers, json=initiate_payload, timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Chat session initiated successfully")
        else:
            print(f"   ❌ Failed to initiate: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"   ❌ Error initiating chat: {e}")
        return False
    
    # Step 2: Wait for initialization
    print("⏳ Step 2: Waiting for system initialization...")
    time.sleep(2)
    
    # Step 3: Send message
    print("💬 Step 3: Sending test message...")
    send_payload = {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": chat_id,
                "id_type": "chat_id",
                "id_resource": "chat"
            },
            "user_info": {
                "UserName": "TestUser"
            }
        },
        "channel": {
            "channel_id": "voice",
            "channel_name": "voice"
        },
        "incoming_events": [{
            "event_template": {
                "event_type": "text",
                "text": "Hello, I need help with my flight"
            }
        }]
    }
    
    try:
        response = requests.post(f"{base_url}/conversation/send_message", 
                               headers=headers, json=send_payload, timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Message sent successfully")
        else:
            print(f"   ❌ Failed to send message: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"   ❌ Error sending message: {e}")
        return False
    
    # Step 4: Get response
    print("📥 Step 4: Polling for agent response...")
    receive_payload = {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": chat_id,
                "id_type": "chat_id",
                "id_resource": "chat"
            },
            "user_info": {
                "UserName": "TestUser"
            }
        },
        "channel": {
            "channel_id": "voice",
            "channel_name": "voice"
        },
        "incoming_events": [{
            "event_template": {
                "event_type": "listen"
            }
        }]
    }
    
    max_attempts = 15  # Increased for service issues
    for attempt in range(max_attempts):
        try:
            response = requests.post(f"{base_url}/conversation/get_message", 
                                   headers=headers, json=receive_payload, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                next_action = data.get("next_action")
                
                print(f"   Attempt {attempt + 1}: next_action = {next_action}")
                
                if next_action == "wait":
                    print("   ⏳ Agent still processing, waiting...")
                    time.sleep(1)
                    continue
                elif next_action in {"say", "gather"}:
                    # Extract responses
                    responses = []
                    response_map = data.get("response_map", {}).get("responses", {}).get("default", [])
                    
                    for resp in response_map:
                        text = resp.get("response_template", {}).get("text")
                        if text:
                            responses.append(text)
                    
                    if responses:
                        print("   ✅ Agent response received!")
                        print(f"   📝 Response: {responses[0][:100]}...")
                        return True
                    else:
                        print("   ⚠️  Response received but no text found")
                        print(f"   📄 Raw response: {json.dumps(data, indent=2)[:300]}...")
                        continue
                elif next_action == "disconnect":
                    print("   🔚 Agent ended conversation")
                    return False
                else:
                    print(f"   ❓ Unknown next_action: {next_action}")
                    continue
            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text[:100]}")
                time.sleep(1)
                continue
                
        except Exception as e:
            print(f"   ❌ Error polling response: {e}")
            time.sleep(1)
            continue
    
    print("   ⏰ Timeout waiting for agent response")
    return False

def main():
    """Run the V1 test."""
    print("🧪 V1 TRIAGE AGENT TEST")
    print("=" * 60)
    
    start_time = datetime.now()
    success = test_v1_triage_agent()
    end_time = datetime.now()
    
    duration = (end_time - start_time).total_seconds()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    print(f"Duration: {duration:.2f} seconds")
    
    if success:
        print("✅ V1 TRIAGE AGENT TEST: PASSED")
        print("   - Chat session created successfully")
        print("   - Message sent and processed")
        print("   - Agent response received")
        print("   - V1 triage agent is working despite service issues")
    else:
        print("❌ V1 TRIAGE AGENT TEST: FAILED")
        print("   - Check Docker logs for detailed error information")
        print("   - Langfuse service may be experiencing issues")
        print("   - Analytics service connection failures detected")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
