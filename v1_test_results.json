{"metadata": {"timestamp": "2025-06-16T22:59:10.482433", "roster_id": "RozieAir", "base_url": "http://localhost", "total_tests": 15}, "analysis": {"summary": {"total_tests": 15, "successful_tests": 15, "failed_tests": 0, "success_rate": 100.0, "avg_execution_time": 4.***************}, "category_breakdown": {"GENERAL": {"total": 15, "success": 15, "failed": 0, "success_rate": 100.0}}, "failed_tests": []}, "detailed_results": [{"test_id": "TEST_001", "category": "GENERAL", "test_type": "single", "user_input": "Hi there, I need some help", "expected_activity": "Provide conversational response before routing, ask clarifying question about specific help needed", "conversation_sequence": null, "chat_id": "TEST_TEST_001_7c0743c1-cd2a-451f-91c2-e26979286cbc", "agent_responses": ["How may I assist you with your travel today? Please select from the following options or let me know if you have a different inquiry:"], "execution_time": 4.***************, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_001_7c0743c1-cd2a-451f-91c2-e26979286cbc", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "How may I assist you with your travel today? Please select from the following options or let me know if you have a different inquiry:"}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_a0e50b4a-9cbd-481b-8597-d61bd537db4e", "label": "Help with flight booking", "priority": 1}, {"id": "context_key_33f0a410-1f9b-4f64-b2dc-1da3f2802239", "label": "Check flight status", "priority": 1}, {"id": "context_key_f00c425f-b4f7-4887-b67a-7a66cb6dfc0a", "label": "Baggage inquiries", "priority": 1}, {"id": "context_key_50d62fdc-8005-4294-93e7-a50b59476d11", "label": "Something else related to travel", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:57:50", "end_time": "2025-06-16T22:57:54", "raw_logs": "2025-06-16T17:27:50.592888513Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:27:50.592934513Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:27:50.592938472Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:27:50.592941013Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:27:50.592943222Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:27:50.647374972Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cdff290>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:27:50.647806430Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cdd75d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:27:51.668792264Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cc0cf10>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:27:50.647374972Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cdff290>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:27:50.647806430Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cdd75d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:27:51.668792264Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cc0cf10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_002", "category": "GENERAL", "test_type": "single", "user_input": "Hello, can you assist me?", "expected_activity": "Respond conversationally with helpful tone, request more details about assistance type", "conversation_sequence": null, "chat_id": "TEST_TEST_002_83ac3ecd-b7f5-468a-9eaa-08099be46d90", "agent_responses": ["Of course! How can I assist you today? Please let me know if you need help with any of the following, or something else:"], "execution_time": 4.667444229125977, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_002_83ac3ecd-b7f5-468a-9eaa-08099be46d90", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Of course! How can I assist you today? Please let me know if you need help with any of the following, or something else:"}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_3587a122-1ec8-4852-9df7-01fc507131b4", "label": "Booking a flight", "priority": 1}, {"id": "context_key_12a3b7c6-9545-4ddd-9e32-6df1d0dbfe31", "label": "Checking your flight status", "priority": 1}, {"id": "context_key_d6c0f825-b61e-41c1-bae4-b585aaf4c9a1", "label": "Baggage queries", "priority": 1}, {"id": "context_key_be766a32-fc81-4674-a9d2-89bf9bdbe616", "label": "Meal pre-booking", "priority": 1}, {"id": "context_key_45c566aa-bf04-4af4-992b-527c287316e3", "label": "Name corrections", "priority": 1}, {"id": "context_key_119a460b-3c80-48a0-9131-29dd0ce284d6", "label": "Adding wheelchair assistance", "priority": 1}, {"id": "context_key_90487a36-e33c-4e6e-b1df-734fe2579432", "label": "Other", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:57:55", "end_time": "2025-06-16T22:58:00", "raw_logs": "2025-06-16T17:27:55.792156960Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:27:55.792224210Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:27:55.792234335Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:27:55.792237085Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:27:55.792391501Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:27:55.849688876Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cc812d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:27:55.850108460Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cca5fd0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:27:56.873223377Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ccc2690>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:27:55.849688876Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cc812d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:27:55.850108460Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cca5fd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:27:56.873223377Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ccc2690>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_003", "category": "GENERAL", "test_type": "single", "user_input": "Good morning, I have a question", "expected_activity": "Acknowledge greeting warmly, ask what specific question they have", "conversation_sequence": null, "chat_id": "TEST_TEST_003_db725a9a-16c3-4a91-b813-0d78f27981fc", "agent_responses": ["Good morning! How may I assist you today? Please let me know your question."], "execution_time": 3.6317861080169678, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_003_db725a9a-16c3-4a91-b813-0d78f27981fc", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Good morning! How may I assist you today? Please let me know your question."}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:01", "end_time": "2025-06-16T22:58:05", "raw_logs": "2025-06-16T17:28:01.532174754Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:01.532253337Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:01.532258421Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:01.532260796Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:01.532268546Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:01.589138046Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cb45850>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:01.589687546Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cb75550>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:02.603919838Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cb7e850>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:01.589138046Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cb45850>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:01.589687546Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cb75550>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:02.603919838Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cb7e850>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_004", "category": "GENERAL", "test_type": "single", "user_input": "Can someone help me please?", "expected_activity": "Respond reassuringly, ask for details about what help is needed", "conversation_sequence": null, "chat_id": "TEST_TEST_004_973f28b7-0115-4b12-a117-7b62d924e40f", "agent_responses": ["Of course, I’m here to help! Could you please tell me a bit more about what you need assistance with regarding your flight or travel?"], "execution_time": 3.6404800415039062, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_004_973f28b7-0115-4b12-a117-7b62d924e40f", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Of course, I’m here to help! Could you please tell me a bit more about what you need assistance with regarding your flight or travel?"}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:06", "end_time": "2025-06-16T22:58:09", "raw_logs": "2025-06-16T17:28:06.242604173Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:06.242668173Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:06.242672673Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:06.242675048Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:06.242676965Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:06.298013090Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ca1b010>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:06.298469173Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ca2cb90>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:07.319120840Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ca32ad0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:06.298013090Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ca1b010>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:06.298469173Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ca2cb90>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:07.319120840Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7ca32ad0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_005", "category": "GENERAL", "test_type": "single", "user_input": "I'm looking for support", "expected_activity": "Provide welcoming response, clarify what type of support is needed", "conversation_sequence": null, "chat_id": "TEST_TEST_005_fe1a712b-9b9c-42cc-81ce-4723ea53a2b7", "agent_responses": ["Could you please specify the type of support you need? For instance, are you looking for help with booking a flight, checking a flight's status, correcting your name on a ticket, dealing with baggage issues, pre-booking a meal, requesting wheelchair assistance, or something else? Let me know so I can route your request appropriately."], "execution_time": 6.208853006362915, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_005_fe1a712b-9b9c-42cc-81ce-4723ea53a2b7", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Could you please specify the type of support you need? For instance, are you looking for help with booking a flight, checking a flight's status, correcting your name on a ticket, dealing with baggage issues, pre-booking a meal, requesting wheelchair assistance, or something else? Let me know so I can route your request appropriately."}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_8829b7ca-a758-436f-bf9a-adaadcd2033c", "label": "Flight booking", "priority": 1}, {"id": "context_key_ebd9cfeb-bc1c-4241-8320-510f9e795dd2", "label": "Flight status", "priority": 1}, {"id": "context_key_a4a0b06b-9d4d-41bb-8194-6494df86c7f4", "label": "Name correction", "priority": 1}, {"id": "context_key_86e97bf0-6c19-480c-990b-540b5cc9dc7a", "label": "Baggage issues", "priority": 1}, {"id": "context_key_081248b7-45dc-4ea0-94c1-c441ff9656b1", "label": "Meal pre-booking", "priority": 1}, {"id": "context_key_58a0de01-b1f9-41f0-91c2-819435b5f709", "label": "Wheelchair assistance", "priority": 1}, {"id": "context_key_1dad7f84-49fd-48bd-bf3e-e7e25e877f37", "label": "Other", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:10", "end_time": "2025-06-16T22:58:17", "raw_logs": "2025-06-16T17:28:10.954915300Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:10.954974009Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:10.954978009Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:10.954980175Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:10.954982175Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:11.008964884Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cae4810>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:11.009475759Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cac88d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:12.031475426Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7caf5cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:11.008964884Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cae4810>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:11.009475759Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7cac88d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:12.031475426Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7caf5cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_006", "category": "GENERAL", "test_type": "single", "user_input": "I have a problem", "expected_activity": "Ask clarifying question about what specific problem they're experiencing", "conversation_sequence": null, "chat_id": "TEST_TEST_006_4eb7f569-94f5-4545-b001-52d6b22ec63d", "agent_responses": ["Sure, I can assist you! Could you specify the issue you're facing? Please select from the options below."], "execution_time": 4.676403284072876, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_006_4eb7f569-94f5-4545-b001-52d6b22ec63d", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Sure, I can assist you! Could you specify the issue you're facing? Please select from the options below."}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_e60b5e8a-b7bb-4eed-ad04-a31d09e8b4b9", "label": "Booking issues", "priority": 1}, {"id": "context_key_d82178b0-f57b-4386-8b97-57024be9f8e2", "label": "Baggage problems", "priority": 1}, {"id": "context_key_7ec1719f-b2e1-4c15-9a83-4db86507310c", "label": "Flight status inquiry", "priority": 1}, {"id": "context_key_bbb073ec-2431-4e3b-bfae-7b7ba46e3529", "label": "Something else", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:18", "end_time": "2025-06-16T22:58:22", "raw_logs": "2025-06-16T17:28:18.240866762Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:18.240933554Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:18.240938137Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:18.240940345Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:18.240942345Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:18.296687929Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c99cc10>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:18.297148762Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c971cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:19.321570263Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c9af4d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:18.296687929Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c99cc10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:18.297148762Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c971cd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:19.321570263Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c9af4d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_007", "category": "GENERAL", "test_type": "single", "user_input": "Something is wrong", "expected_activity": "Request more details about what specifically is wrong", "conversation_sequence": null, "chat_id": "TEST_TEST_007_a0d5f61f-01d7-4840-bd96-81e2d21becd6", "agent_responses": ["Could you please provide more details about the issue you're experiencing? Are you having trouble with your flight booking, baggage, check-in, meal prebooking, or something else? This will help me assist you better."], "execution_time": 5.699524879455566, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_007_a0d5f61f-01d7-4840-bd96-81e2d21becd6", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Could you please provide more details about the issue you're experiencing? Are you having trouble with your flight booking, baggage, check-in, meal prebooking, or something else? This will help me assist you better."}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_cf7d0801-0960-49a5-894d-a0051c38e222", "label": "Flight booking", "priority": 1}, {"id": "context_key_753eada6-80f4-4b47-a971-6831872e0954", "label": "Baggage", "priority": 1}, {"id": "context_key_7b1ef368-f4b2-401f-b7fe-144487236a2c", "label": "Check-in", "priority": 1}, {"id": "context_key_adacf3c8-c66d-429f-b936-54636c72e96a", "label": "Meal prebooking", "priority": 1}, {"id": "context_key_bdc050f2-b075-4444-abbf-5bd6ee537824", "label": "Something else", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:23", "end_time": "2025-06-16T22:58:29", "raw_logs": "2025-06-16T17:28:23.986422876Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:23.986475584Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:23.986479417Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:23.986481334Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:23.986499542Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:24.040106792Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c861450>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:24.040597834Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c9bbbd0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:25.062846459Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c86e1d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:24.040106792Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c861450>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:24.040597834Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c9bbbd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:25.062846459Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c86e1d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_008", "category": "GENERAL", "test_type": "single", "user_input": "I need help with something", "expected_activity": "Ask what specific thing they need help with", "conversation_sequence": null, "chat_id": "TEST_TEST_008_477fa2bf-f178-4505-b91d-b03d447f592b", "agent_responses": ["Of course! Can you please tell me more about what you need help with? For example, are you looking for information about your flight, booking tickets, baggage, meals, assistance, or something else?"], "execution_time": 4.667630910873413, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_008_477fa2bf-f178-4505-b91d-b03d447f592b", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Of course! Can you please tell me more about what you need help with? For example, are you looking for information about your flight, booking tickets, baggage, meals, assistance, or something else?"}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_a04af46c-4952-4ad0-a317-9e833af0f44d", "label": "Flight information", "priority": 1}, {"id": "context_key_724d0402-b1cf-4278-8bba-d359146e0460", "label": "Booking tickets", "priority": 1}, {"id": "context_key_fb03e011-4d1f-48d3-8922-2c2dd82cf2b1", "label": "Baggage", "priority": 1}, {"id": "context_key_39ecad2e-dd9b-41f2-bb0b-fb4d38f5a817", "label": "Meals", "priority": 1}, {"id": "context_key_175f8a64-e076-444a-a676-1ab1852887ce", "label": "Assistance", "priority": 1}, {"id": "context_key_0de6d82a-a6cb-47bd-b44d-eba62879eef4", "label": "Something else", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:30", "end_time": "2025-06-16T22:58:35", "raw_logs": "2025-06-16T17:28:30.737695212Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:30.737745337Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:30.737748045Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:30.737749629Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:30.737751212Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:30.794577087Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c7285d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:30.794968754Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c8c4450>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:31.815994838Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c73b450>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:30.794577087Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c7285d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:30.794968754Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c8c4450>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:31.815994838Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c73b450>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_009", "category": "GENERAL", "test_type": "single", "user_input": "There's an issue", "expected_activity": "Clarify what type of issue and provide examples if needed", "conversation_sequence": null, "chat_id": "TEST_TEST_009_fac708a6-b5f5-4031-adf8-532afbefc9f5", "agent_responses": ["Could you please provide more details about the issue you're experiencing? This will help me assist you more effectively. Are you having trouble with your booking, baggage, flight status, or something else?"], "execution_time": 4.156598091125488, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_009_fac708a6-b5f5-4031-adf8-532afbefc9f5", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Could you please provide more details about the issue you're experiencing? This will help me assist you more effectively. Are you having trouble with your booking, baggage, flight status, or something else?"}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_64ab9962-ced9-444a-be8d-33c6a97530f5", "label": "Booking", "priority": 1}, {"id": "context_key_233792e3-ecb4-4cf8-8602-10bab2a80870", "label": "Baggage", "priority": 1}, {"id": "context_key_44b226cd-5e19-4426-9e03-8865cab7534c", "label": "Flight status", "priority": 1}, {"id": "context_key_d1043a23-8d36-415f-bb18-9da4cac3a93e", "label": "Something else", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:36", "end_time": "2025-06-16T22:58:40", "raw_logs": "2025-06-16T17:28:36.484947632Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:36.485017548Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:36.485022215Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:36.485024673Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:36.485026673Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:36.542589298Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c79c190>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:36.542980215Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c718b50>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:37.566948965Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c7f6190>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:36.542589298Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c79c190>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:36.542980215Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c718b50>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:37.566948965Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c7f6190>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_010", "category": "GENERAL", "test_type": "single", "user_input": "Can you fix this?", "expected_activity": "Ask what specifically needs to be fixed", "conversation_sequence": null, "chat_id": "TEST_TEST_010_42d8cf78-fd3f-467a-85ea-fc2d8bf80a9d", "agent_responses": ["Could you please clarify what you would like me to fix? Are you referring to a flight booking, a name on your ticket, baggage, or something else? Let me know a bit more so I can assist you accurately."], "execution_time": 3.6356379985809326, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_010_42d8cf78-fd3f-467a-85ea-fc2d8bf80a9d", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Could you please clarify what you would like me to fix? Are you referring to a flight booking, a name on your ticket, baggage, or something else? Let me know a bit more so I can assist you accurately."}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:41", "end_time": "2025-06-16T22:58:45", "raw_logs": "2025-06-16T17:28:41.701256217Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:41.701355009Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:41.701361759Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:41.701363926Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:41.701365884Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:41.760447676Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c67ead0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:41.760866384Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c6a45d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:42.788413676Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c6a5810>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:41.760447676Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c67ead0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:41.760866384Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c6a45d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:42.788413676Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c6a5810>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_011", "category": "GENERAL", "test_type": "single", "user_input": "What's the weather like in Paris?", "expected_activity": "Graceful fallback explaining limitation, offer airline-related assistance instead", "conversation_sequence": null, "chat_id": "TEST_TEST_011_911ab049-e622-41c8-a022-f5cc7a8bde35", "agent_responses": ["I wasn’t able to find specific details in our knowledge base to answer that question."], "execution_time": 3.1251449584960938, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_011_911ab049-e622-41c8-a022-f5cc7a8bde35", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "I wasn’t able to find specific details in our knowledge base to answer that question."}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:46", "end_time": "2025-06-16T22:58:49", "raw_logs": "2025-06-16T17:28:46.421370095Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:46.421435261Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:46.421439761Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:46.421441928Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:46.421443803Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:46.477701345Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c536410>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:46.478086803Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c537e90>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:47.505417678Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c562c10>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:46.477701345Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c536410>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:46.478086803Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c537e90>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:47.505417678Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c562c10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_012", "category": "GENERAL", "test_type": "single", "user_input": "Can you book me a hotel?", "expected_activity": "Politely explain out-of-scope, suggest flight booking or airline services", "conversation_sequence": null, "chat_id": "TEST_TEST_012_48936661-5b33-4381-9959-56cef73b3d97", "agent_responses": ["I’m currently unable to assist with hotel bookings, but I can help you find and book flights. Would you like to search for flights or do you need assistance with something else related to your travel?"], "execution_time": 4.665325880050659, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_012_48936661-5b33-4381-9959-56cef73b3d97", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "I’m currently unable to assist with hotel bookings, but I can help you find and book flights. Would you like to search for flights or do you need assistance with something else related to your travel?"}}, {"response_template": {"response_type": "quick_reply", "items": [{"id": "context_key_e158d679-4a9c-4534-9833-4d0701f1dafd", "label": "Search for flights", "priority": 1}, {"id": "context_key_b3617040-bc71-48e9-8c72-f0806b16898e", "label": "Get help with another travel-related issue", "priority": 1}]}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:50", "end_time": "2025-06-16T22:58:55", "raw_logs": "2025-06-16T17:28:50.616398097Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:50.616431555Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:50.616488055Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:50.616495555Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:50.616797305Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:50.675426138Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c5f9810>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:50.675927680Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c5ae9d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:51.693420180Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c753210>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:50.675426138Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c5f9810>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:50.675927680Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c5ae9d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:51.693420180Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c753210>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_013", "category": "GENERAL", "test_type": "single", "user_input": "I need car rental information", "expected_activity": "Acknowledge request, explain limitation, offer relevant airline services", "conversation_sequence": null, "chat_id": "TEST_TEST_013_27f0f944-ea02-44f5-b15a-d6340a8d4d62", "agent_responses": ["I wasn’t able to find specific details in our knowledge base to answer that question."], "execution_time": 3.644637107849121, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_013_27f0f944-ea02-44f5-b15a-d6340a8d4d62", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "I wasn’t able to find specific details in our knowledge base to answer that question."}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:58:56", "end_time": "2025-06-16T22:58:59", "raw_logs": "2025-06-16T17:28:56.362737668Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:28:56.362800627Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:28:56.362805168Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:28:56.362807335Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:28:56.362809293Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:28:56.419981918Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c445290>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:56.420448793Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c48fbd0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:28:57.443468169Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c4c8fd0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:28:56.419981918Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c445290>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:56.420448793Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c48fbd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:28:57.443468169Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c4c8fd0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_014", "category": "GENERAL", "test_type": "single", "user_input": "Help with my travel insurance", "expected_activity": "Graceful fallback, suggest contacting insurance provider, offer flight-related help", "conversation_sequence": null, "chat_id": "TEST_TEST_014_d92d6eb5-9c28-4272-9b0a-f88791e0760f", "agent_responses": ["Could you please specify what kind of assistance you need with your travel insurance? For example, are you looking for information about coverage, how to file a claim, or something else? This will help me better assist you."], "execution_time": 4.151224136352539, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_014_d92d6eb5-9c28-4272-9b0a-f88791e0760f", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "Could you please specify what kind of assistance you need with your travel insurance? For example, are you looking for information about coverage, how to file a claim, or something else? This will help me better assist you."}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:59:01", "end_time": "2025-06-16T22:59:05", "raw_logs": "2025-06-16T17:29:01.082337379Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:29:01.082422337Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:29:01.082428629Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:29:01.082431087Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:29:01.082433587Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:29:01.137529629Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c336b10>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:29:01.137937087Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c35b9d0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:29:02.160446338Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c380350>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:29:01.137529629Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c336b10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:29:01.137937087Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c35b9d0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:29:02.160446338Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c380350>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}, {"test_id": "TEST_015", "category": "GENERAL", "test_type": "single", "user_input": "Restaurant recommendations in Tokyo", "expected_activity": "Polite limitation explanation, redirect to airline services available", "conversation_sequence": null, "chat_id": "TEST_TEST_015_4bdfd7a3-6a7f-4f14-85c7-f7fdbdf41182", "agent_responses": ["I wasn’t able to find specific details in our knowledge base to answer that question."], "execution_time": 3.126899003982544, "success": true, "error_message": null, "raw_responses": [{"version": "1.0", "rozie_user_id": {}, "user_info": {"user_id": {"id": "TEST_TEST_015_4bdfd7a3-6a7f-4f14-85c7-f7fdbdf41182", "id_type": "chat_id", "id_resource": "chat"}, "user_info": {"UserName": "TestUser"}}, "response_map": {"responses": {"default": [{"response_template": {"response_type": "text", "text": "I wasn’t able to find specific details in our knowledge base to answer that question."}}]}}, "should_end_interaction": false, "next_action": "gather"}], "docker_logs": {"enabled": true, "start_time": "2025-06-16T22:59:06", "end_time": "2025-06-16T22:59:09", "raw_logs": "2025-06-16T17:29:06.307440506Z 🔍 [CONVERSATION_MANAGER] Checking triage agent version...\n2025-06-16T17:29:06.307505256Z 📋 [CONVERSATION_MANAGER] Roster ID: RozieAir\n2025-06-16T17:29:06.307509673Z 🎛️  [CONVERSATION_MANAGER] use_triage_agent_v2 setting: False\n2025-06-16T17:29:06.307512048Z 📝 [CONVERSATION_MANAGER] Using Original Triage Agent V1 (Legacy)\n2025-06-16T17:29:06.307514006Z ✅ [CONVERSATION_MANAGER] Triage agent wrapper created: TriageAgentWrapper\n2025-06-16T17:29:06.362426298Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c214650>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:29:06.362858881Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c216c10>: Failed to establish a new connection: [Errno 111] Connection refused'))\n2025-06-16T17:29:07.385436382Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c236210>: Failed to establish a new connection: [Errno 111] Connection refused'))\n", "parsed_analysis": {"triage_agent_activity": [], "routing_decisions": [], "specialist_usage": [], "llm_interactions": [], "delegate_tools": [], "error_messages": [{"type": "error", "log": "2025-06-16T17:29:06.362426298Z Failed to add New User Created analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c214650>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:29:06.362858881Z Failed to add Conversation Started analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c216c10>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, {"type": "error", "log": "2025-06-16T17:29:07.385436382Z Failed to add Request Received analytics event for application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3 - Exception: HTTPConnectionPool(host='0.0.0.0', port=8000): Max retries exceeded with url: /api/v1/internal-events (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff7c236210>: Failed to establish a new connection: [Errno 111] Connection refused'))"}]}}}]}